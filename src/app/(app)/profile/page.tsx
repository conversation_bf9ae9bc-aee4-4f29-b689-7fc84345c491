'use client';

import { useState } from 'react';

import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';

import { ProfileReferralSection } from './components/profile-referral-section';
import { ProfileUserInfo } from './components/profile-user-info';
import { ProfileWalletSection } from './components/profile-wallet-section';
import { TransactionHistoryTable } from './components/transaction-history-table';

export default function Profile() {
  const [activeTab, setActiveTab] = useState('main');

  return (
    <div className="max-w-4xl mx-auto space-y-4">
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="main">Main</TabsTrigger>
          <TabsTrigger value="transactions">My Transactions</TabsTrigger>
        </TabsList>

        <TabsContent value="main" className="space-y-2">
          <ProfileUserInfo />
          <ProfileWalletSection />
          <ProfileReferralSection />
        </TabsContent>

        <TabsContent value="transactions" className="space-y-4">
          <TransactionHistoryTable />
        </TabsContent>
      </Tabs>
    </div>
  );
}
