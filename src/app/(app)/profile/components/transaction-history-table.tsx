'use client';

import type { DocumentSnapshot } from 'firebase/firestore';
import { Loader2 } from 'lucide-react';
import { useCallback, useEffect, useState } from 'react';

import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import type { UserTxEntity, TxType } from '@/constants/core.constants';
import { useInfiniteScroll } from '@/hooks/use-infinite-scroll';
import { useRootContext } from '@/root-context';
import { getUserTransactionHistory } from '@/services/transaction-history-service';
import { firebaseTimestampToDate } from '@/utils/date-utils';

interface TransactionHistoryTableProps {}

export function TransactionHistoryTable({}: TransactionHistoryTableProps) {
  const { currentUser } = useRootContext();
  const [transactions, setTransactions] = useState<UserTxEntity[]>([]);
  const [loading, setLoading] = useState(false);
  const [loadingMore, setLoadingMore] = useState(false);
  const [hasMore, setHasMore] = useState(true);
  const [lastDoc, setLastDoc] = useState<DocumentSnapshot | null>(null);

  const loadTransactions = useCallback(
    async (isLoadMore = false) => {
      if (!currentUser?.id) return;

      if (isLoadMore) {
        setLoadingMore(true);
      } else {
        setLoading(true);
        setTransactions([]);
        setLastDoc(null);
      }

      try {
        const result = await getUserTransactionHistory({
          userId: currentUser.id,
          limit: 20,
          lastDoc: isLoadMore ? lastDoc : null,
        });

        if (isLoadMore) {
          setTransactions((prev) => {
            // Deduplicate transactions by ID
            const existingIds = new Set(prev.map((tx) => tx.id));
            const newTransactions = result.transactions.filter(
              (tx) => !existingIds.has(tx.id),
            );
            return [...prev, ...newTransactions];
          });
        } else {
          setTransactions(result.transactions);
        }

        setLastDoc(result.lastDoc);
        setHasMore(result.hasMore);
      } catch (error) {
        console.error('Error loading transaction history:', error);
      } finally {
        setLoading(false);
        setLoadingMore(false);
      }
    },
    [currentUser?.id, lastDoc],
  );

  const loadMoreRef = useInfiniteScroll({
    hasMore,
    loading: loading || loadingMore,
    onLoadMore: () => loadTransactions(true),
  });

  useEffect(() => {
    loadTransactions();
  }, [currentUser?.id]);

  const formatTxType = (txType: TxType): string => {
    switch (txType) {
      case 'deposit':
        return 'Deposit';
      case 'withdraw':
        return 'Withdraw';
      case 'buy_lock_collateral':
        return 'Buy Lock';
      case 'unlock_collateral':
        return 'Unlock';
      case 'sell_lock_collateral':
        return 'Sell Lock';
      case 'referral_fee':
        return 'Referral Fee';
      case 'cancelation_fee':
        return 'Cancel Fee';
      case 'refund':
        return 'Refund';
      case 'sell_fulfillment':
        return 'Fulfillment';
      case 'resell_fee_earnings':
        return 'Resell Earnings';
      default:
        return txType;
    }
  };

  const formatAmount = (amount: number): string => {
    const sign = amount >= 0 ? '+' : '';
    return `${sign}${amount.toFixed(2)} TON`;
  };

  const getAmountColor = (amount: number): string => {
    return amount >= 0 ? 'text-green-600' : 'text-red-600';
  };

  const formatDate = (date: Date | undefined) => {
    if (!date) return '';
    return firebaseTimestampToDate(date).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  if (loading) {
    return (
      <div className="flex justify-center py-12">
        <Loader2 className="w-8 h-8 animate-spin text-[#708499]" />
      </div>
    );
  }

  if (transactions.length === 0) {
    return (
      <div className="text-center py-12">
        <p className="text-[#708499] text-lg">No transactions found</p>
        <p className="text-[#708499] text-sm mt-2">
          Your transaction history will appear here
        </p>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <div className="bg-[#232e3c] border border-[#3a4a5c] rounded-lg overflow-hidden">
        <Table>
          <TableHeader>
            <TableRow className="border-[#3a4a5c]">
              <TableHead className="text-[#f5f5f5]">Type</TableHead>
              <TableHead className="text-[#f5f5f5]">Amount</TableHead>
              <TableHead className="text-[#f5f5f5]">Date</TableHead>
              <TableHead className="text-[#f5f5f5]">Description</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {transactions.map((transaction) => (
              <TableRow
                key={transaction.id}
                className="border-[#3a4a5c] hover:bg-[#2a3441]"
              >
                <TableCell className="text-[#f5f5f5]">
                  {formatTxType(transaction.tx_type)}
                </TableCell>
                <TableCell className={getAmountColor(transaction.amount)}>
                  {formatAmount(transaction.amount)}
                </TableCell>
                <TableCell className="text-[#708499]">
                  {formatDate(transaction.createdAt)}
                </TableCell>
                <TableCell className="text-[#708499] max-w-xs truncate">
                  {transaction.description || '-'}
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>

      {loadingMore && (
        <div className="flex justify-center py-4">
          <Loader2 className="w-6 h-6 animate-spin text-[#708499]" />
        </div>
      )}

      <div ref={loadMoreRef} className="h-1" />
    </div>
  );
}
